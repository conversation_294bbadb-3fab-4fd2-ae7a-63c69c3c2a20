  function setCookie(name, value) {
      const days = 5;
      const expires = new Date(Date.now() + days * 864e5).toUTCString();
      document.cookie = `${name}=${encodeURIComponent(value)}; secure; samesite=strict; expires=${expires}; path=/;`;
    }

    function sanitizeParams(params, whitelist) {
      let result = [];
      for (const [key, value] of params.entries()) {
        if (whitelist.includes(key)) {
          result.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
      }
      return result.join("&");
    }

    async function pageOnload() {
      const params = new URLSearchParams(window.location.search);
      const app = params.get("app");

      if (!app) return;

      const response = await fetch("/src/configs/apps.json");
      const appInfos = await response.json();

      if (!appInfos.hasOwnProperty(app)) return;

      const cookieName = `_dm_dplnk_params_${app}`;
      const allowedParams = ["news_id", "app"]; // Whitelist
      const deepLinkParams = sanitizeParams(params, allowedParams);

      if (deepLinkParams) {
        setCookie(cookieName, deepLinkParams);
       // window.location.href = `${appInfos[app].storeUrl}?${deepLinkParams}`;
      } else {
       // window.location.href = `${appInfos[app].storeUrl}`;
      }
    }