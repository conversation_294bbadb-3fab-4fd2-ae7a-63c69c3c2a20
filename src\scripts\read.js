 function getCookie(name) {
      const match = document.cookie.match('(^|;)\\s*' + name + '\\s*=\\s*([^;]+)');
      return match ? decodeURIComponent(match[2]) : null;
    }

    function deleteCookie(name) {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; Secure; SameSite=Strict`;
    }

    async function pageOnload() {
      const params = new URLSearchParams(window.location.search);
      const app = params.get("app");

      if (!app) return;

      const response = await fetch("/src/configs/apps.json");
      const appInfos = await response.json();

      if (!appInfos.hasOwnProperty(app)) return;

      const cookieName = `_dm_dplnk_params_${app}`;
      const cookieValue = getCookie(cookieName);
      let finalUrl = appInfos[app].deepLink;

      if (cookieValue) {
        finalUrl += `?${cookieValue}`;
        deleteCookie(cookieName);
      }

      // Gü<PERSON>li yönlendirme
      const link = document.createElement("a");
      link.href = finalUrl;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
    }